# Test Plan: TikTok Audience Filter Reset Fix

## Vấn đề đã được giải quyết:

1. **FilterPanel không đồng bộ với filterPayload**: Đ<PERSON> thêm useEffect để đồng bộ search state với filterPayload.search
2. **DateRangePicker không reset**: Đã cập nhật để reset khi initialDateFrom và initialDateTo trở thành empty
3. **Query không refetch khi filterPayload giống nhau**: Đã thêm refreshKey để force refetch

## Các thay đổi đã thực hiện:

### 1. FilterPanel.tsx
- Thêm useEffect để đồng bộ search state với filterPayload.search
- Thêm initialDateFrom và initialDateTo props cho DateRangePicker

### 2. DateRangerPicker.tsx  
- Cập nhật useEffect để reset range khi cả initialDateFrom và initialDateTo đều empty

### 3. CustomTiktokAudiences.tsx
- Thêm refreshKey vào queryKey để force refetch
- Thêm useEffect để detect khi filterPayload được reset về default values
- Cập nhật query để include ad_account_id trong params

### 4. HeaderTiktok.tsx
- Thêm queryClient.invalidateQueries khi upload audience thành công

### 5. ContainerTiktokAds.tsx
- Thêm wrapper function để handle setFilterPayload

## Test Cases:

### Test Case 1: Filter Reset sau khi upload audience
1. Mở trang TikTok Audience
2. Nhập search term và chọn date range
3. Upload một audience mới
4. Verify: Search input và date picker đều được reset về empty
5. Verify: Query được refetch với filterPayload mới

### Test Case 2: Filter Reset khi chuyển account
1. Có filter đang active (search + date)
2. Chuyển sang account khác
3. Verify: UI được reset và query refetch với account mới

### Test Case 3: Pagination với filter empty
1. Đảm bảo filterPayload ở trang 1, không có search/filter
2. Thay đổi pagination
3. Verify: Query vẫn được trigger đúng cách

### Test Case 4: Manual filter reset
1. Set một số filter
2. Manually reset filterPayload về default values
3. Verify: UI được cập nhật và query refetch

## Expected Behavior:
- FilterPanel UI luôn đồng bộ với filterPayload state
- Query được refetch khi cần thiết, ngay cả khi filterPayload giống nhau
- DateRangePicker reset đúng cách khi filterPayload được reset
- Không có duplicate API calls không cần thiết
