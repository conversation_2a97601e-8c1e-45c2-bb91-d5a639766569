import { SuspenseWrapper } from '@/components/SuspenseWrap';
import { TIKTOK_ADS_ROUTER } from '@/constants/router';
import { lazy } from 'react';
import { RouteObject } from 'react-router-dom';
import { TiktokAuthProvider } from '@/pages/TiktokAds/context/TiktokAuthContext';

const TikTokPage = lazy(() => import('@/pages/TiktokAds'));

const useTiktokAdsRouter = (): RouteObject[] => [
  {
    path: TIKTOK_ADS_ROUTER[''],
    element: (
      <SuspenseWrapper
        component={
          <TiktokAuthProvider>
            <TikTokPage />
          </TiktokAuthProvider>
        }
      />
    ),
  },
];

export default useTiktokAdsRouter;
