import { cn } from '@/lib/utils';
import React, { HTMLInputTypeAttribute } from 'react';
import { RiSearchLine } from '@remixicon/react';

type Props = {
  value: string;
  setSearchQuery: (value: string) => void;
  className?: string;
  iconSize?: number;
  placeholder?: string;
  icon?: React.ReactNode;
  type?: HTMLInputTypeAttribute;
};

const SearchBar = ({
  value,
  setSearchQuery,
  className = '',
  iconSize = 14,
  placeholder = '',
  icon,
  type = 'text',
}: Props) => {
  return (
    <div className={cn('flex items-center p-2 border border-tertiary rounded-xl gap-1', className)}>
      {icon ?? <RiSearchLine size={iconSize} color={'#6B7183'} />}
      <input
        type={type}
        className="outline-none flex-1 flex-shrink-0 min-w-0 placeholder:text-filter placeholder:text-sm"
        onChange={(value) => setSearchQuery(value.target.value)}
        placeholder={placeholder}
        value={value}
      />
    </div>
  );
};
export default SearchBar;
