import Modal from '@/components/Modal';
import { Button } from '@/components/ui/button';
import { RiAddCircleLine } from '@remixicon/react';
import { t } from 'i18next';
import { useState } from 'react';
import AddAudienceView from './AddAudienceView';

interface IAddAudiencePopup {
  onSubmitAddSegment: (payload: { audience_name: string; segment_id: string }) => void;
  loading: boolean;
}

const AddAudiencePopup = ({ ...props }: IAddAudiencePopup) => {
  const { onSubmitAddSegment, loading } = props;
  const [open, setOpen] = useState<boolean>(false);
  const toggleOpen = () => setOpen(!open);
  return (
    <Modal
      openModal={open}
      onOpenChange={setOpen}
      isCloseIcon={false}
      className="max-w-[650px]"
      trigger={
        <Button className="bg-[#14161D] hover:bg-[#14161D]/90 px-2 py-1 rounded-xl" size={'lg'}>
          <RiAddCircleLine />
          {t('common.button.addCustomAudience')}
        </Button>
      }
    >
      <AddAudienceView
        toggleOpen={toggleOpen}
        onSubmitAddSegment={onSubmitAddSegment}
        loading={loading}
      />
    </Modal>
  );
};
export default AddAudiencePopup;
