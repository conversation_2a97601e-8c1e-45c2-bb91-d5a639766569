import { Switch } from '@/components/ui/switch';
import { RiLoader2Line } from '@remixicon/react';
import { useState } from 'react';

export type BudgetCellProps = {
  value: boolean;
  onToggleStatus: (value: boolean) => Promise<boolean>;
};

const ToggleStatusCel = ({ value, onToggleStatus }: BudgetCellProps) => {
  const [statusValue, setStatusValue] = useState(value);
  const [loading, setLoading] = useState(false);

  const handleChangeStatus = async (checked: boolean) => {
    setLoading(true);
    const result = await onToggleStatus(checked).then((res)=>{
      return res;
    }).finally(() => {
      setLoading(false);
    });
    if (result) {
      setStatusValue(checked);
    } else {
      setStatusValue(value);
    }
  };

  return loading ? (
    <RiLoader2Line className="animate-spin" size={24} />
  ) : (
    <Switch checked={statusValue} onCheckedChange={handleChangeStatus} />
  );
};

export default ToggleStatusCel;
