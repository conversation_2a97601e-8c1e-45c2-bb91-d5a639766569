import { Button } from '@/components/ui/button';
import { useSegmentContext } from '@/pages/context/SegmentContext';
import { RiInformation2Line, RiLoader2Line } from '@remixicon/react';
import { useState } from 'react';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useTranslation } from 'react-i18next';
import LabelCustom from '@/components/Label';

type Props = {
  toggleOpen: () => void;
  onSubmitAddSegment: (payload: { audience_name: string; segment_id: string }) => void;
  loading: boolean;
};

const AddAudienceView = ({ toggleOpen, onSubmitAddSegment, loading }: Props) => {
  const { items } = useSegmentContext();
  const { t } = useTranslation();
  const [payload, setPayload] = useState<{
    audience_name: string;
    segment_id: string;
  }>({ audience_name: '', segment_id: '' });

  const newOptions = items.reduce<{ label: string; value: string }[]>((acc, item) => {
    if (item.contact_quantity > 0) {
      console.log('item', item);
      acc.push({
        label: item.name,
        value: item.id,
      });
    }
    return acc;
  }, []);
  const segmentSelected = items.find((item) => item.id === payload.segment_id);

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center flex-col">
        <span className="text-lg leading-8 font-medium tracking-[0.6px]">
          {t('audience.addCustomAudience')}
        </span>
        <span className="text-secondary text-sm">
          {t('audiences.chooseSegment')}
        </span>
      </div>
      <div className="flex flex-col">
        <LabelCustom
          isRequire={true}
          className="mb-1"
          label={t('common.facebookAds.audiences.segment')}
        />
        <Select onValueChange={(value) => setPayload((prev) => ({ ...prev, segment_id: value }))}>
          <SelectTrigger className="w-full h-10 rounded-xl">
            <SelectValue placeholder={t('common.facebookAds.audiences.segmentPlaceholder')} />
          </SelectTrigger>
          <SelectContent className="max-h-[250px] overflow-auto p-2 rounded-xl">
            {newOptions.map((item) => (
              <SelectItem
                className={cn(
                  'text-sm p-2 rounded-md cursor-pointer',
                  item.value === payload.segment_id &&
                    '!bg-brand text-white hover:text-white focus:text-white',
                )}
                key={item.value}
                value={item.value}
              >
                {item.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
        {payload.segment_id && (
          <div className="flex items-center gap-1 mt-2">
            <span className="text-sm text-secondary">{t('common.facebookAds.totalContact')}:</span>
            <span className="font-medium">
              {Number(segmentSelected?.contact_quantity).toLocaleString()}
            </span>
          </div>
        )}
      </div>
      <div className="flex flex-col">
        <LabelCustom className="mb-1" label={t('segment.title')} />
        <input
          value={payload.audience_name}
          onChange={(e) => setPayload((prev) => ({ ...prev, audience_name: e.target.value }))}
          className={cn('outline-none border h-10 w-full p-3 rounded-xl text-sm')}
          placeholder={t('segment.selectSegment')}
        />
        <div className="flex items-center gap-1 text-xs text-secondary mt-2">
          <RiInformation2Line size={16} />
          {t('common.facebookAds.audiences.notice')}
        </div>
      </div>
      <div className="flex items-end justify-end gap-3">
        <Button
          onClick={toggleOpen}
          className="px-3 py-1 rounded-xl"
          variant={'secondary'}
          size={'lg'}
        >
          {t('common.button.cancel')}
        </Button>
        <Button
          onClick={() => onSubmitAddSegment(payload)}
          disabled={!Boolean(segmentSelected && payload.audience_name)}
          className="px-3 py-1 rounded-xl min-w-[144px]"
          size={'lg'}
        >
          {loading ? (
            <RiLoader2Line className="animate-spin" />
          ) : (
            t('common.facebookAds.audiences.pushToFb')
          )}
        </Button>
      </div>
    </div>
  );
};
export default AddAudienceView;

