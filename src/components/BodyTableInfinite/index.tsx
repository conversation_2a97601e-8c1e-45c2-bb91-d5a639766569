import { useCallback, useEffect, useRef } from 'react';
import { flexRender } from '@tanstack/react-table';
import type { TBodyTableProps } from '@/types/table';
import { TableBody, TableCell, TableRow } from '@/components/ui/table';
import { useTranslation } from 'react-i18next';
import { RiLoader2Line } from '@remixicon/react';
import { StorageIcon } from '@/assets/StorageIcon';

const BodyTableInfinite = <T extends object>({
  table,
  columns,
  isFetching,
  isLoading,
  className,
  fetchNextPage,
  scrollContainerRef,
  syncNoData,
}: TBodyTableProps<T>) => {
  const { t } = useTranslation();
  const tableBodyRef = useRef<HTMLTableSectionElement>(null);

  const isCallingRef = useRef(false);

  const fetchMoreOnBottomReached = useCallback(() => {
    const container = scrollContainerRef?.current;
    if (!container || isFetching || isCallingRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = container;
    const nearBottom = scrollHeight - scrollTop - clientHeight < 120;

    if (nearBottom && fetchNextPage) {
      isCallingRef.current = true;
      fetchNextPage().finally(() => {
        setTimeout(() => {
          isCallingRef.current = false;
        }, 300);
      });
    }
  }, [scrollContainerRef, fetchNextPage, isFetching]);

  useEffect(() => {
    const container = scrollContainerRef?.current;
    if (!container) return;

    container.addEventListener('scroll', fetchMoreOnBottomReached);
    return () => {
      container.removeEventListener('scroll', fetchMoreOnBottomReached);
    };
  }, [fetchMoreOnBottomReached, scrollContainerRef]);

  const { rows } = table.getRowModel();

  return (
    <TableBody ref={tableBodyRef} className={className ?? 'min-h-[200px]'}>
      {rows.length > 0 ? (
        <>
          {rows.map((row) => (
            <TableRow key={row.id} className="border-b border-tertiary">
              {row.getVisibleCells().map((cell) => (
                <TableCell
                  key={cell.id}
                  className="px-0 py-1 font-normal text-sm text-primary h-[48px] text-left"
                >
                  {flexRender(cell.column.columnDef.cell, cell.getContext())}
                </TableCell>
              ))}
            </TableRow>
          ))}

          {isFetching && (
            <TableRow>
              <TableCell colSpan={columns.length} className="p-2 text-center">
                <RiLoader2Line className="animate-spin mx-auto" size={20} />
              </TableCell>
            </TableRow>
          )}
        </>
      ) : isLoading ? (
        <TableRow>
          <TableCell colSpan={columns.length} className="text-center">
            <div className="flex items-center justify-center p-4">
              <RiLoader2Line className="animate-spin" size={24} />
            </div>
          </TableCell>
        </TableRow>
      ) : (
        <TableRow className="hover:bg-transparent">
          {syncNoData ? (
            <TableCell colSpan={columns.length} className="h-96 text-center hover:bg-transparent">
              <div className="w-[80px] mx-auto mb-4 aspect-square rounded-full flex items-center justify-center bg-secondary-foreground_crm">
                <StorageIcon />
              </div>
              <p className="font-bold text-base text-primary-crm mb-2">{t('common.noData')}</p>
              <p className="font-normal text-sm text-secondary">{t('common.noDataDescriptions')}</p>
            </TableCell>
          ) : (
            <TableCell colSpan={columns.length} className="text-center">
              <span className="font-bold">{t('common.noData')}</span>
            </TableCell>
          )}
        </TableRow>
      )}
    </TableBody>
  );
};

export default BodyTableInfinite;
