import { StorageIcon } from '@/assets/StorageIcon';
import { t } from 'i18next';

const EmptyDataScreen = () => (
  <div className="h-96 text-center flex flex-col items-center justify-center hover:bg-transparent">
    <div className="w-[80px] mx-auto mb-4 aspect-square rounded-full flex items-center justify-center bg-secondary-foreground_crm">
      <StorageIcon />
    </div>
    <p className="font-bold text-base text-primary-crm mb-2">{t('common.noData')}</p>
    <p className="font-normal text-sm text-secondary">{t('common.noDataDescriptions')}</p>
  </div>
);

export default EmptyDataScreen;
