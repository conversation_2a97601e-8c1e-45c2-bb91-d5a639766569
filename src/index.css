@import './App.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --brand: 259, 100%, 68%, 1;
    --brand-secondary: 256, 100%, 76%;
    --brand-foreground: 253, 100%, 93%, 1;
    --background: 0 0% 100%;
    --background-foreground: 0 0% 99%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary-crm: 225, 16%, 15%, 1;
    --primary-foreground-crm: 0 0% 98%;
    --hover-filter: 266, 78%, 36%;
    --hover-filter-foreground: 268, 78%, 93%;
    --text-primary-crm: 264, 78%, 42%;
    --text-foreground: 253, 100%, 93%, 1;
    --secondary: 206, 4%, 32%, 1;
    --secondary-foreground-crm: 0, 0%, 94%, 1;
    --tertiary: 210, 3%, 89%, 1;
    --tertiary-foreground: 226, 12%, 36%, 1;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 210, 3%, 89%, 1;
    --input: 0 0% 89.8%;
    --avatar: 0, 0%, 85%;
    --ring: 0 0% 3.9%;
    --create: 227, 18%, 10%;
    --create-foreground: 227, 14%, 25%;
    --error: 0, 100%, 93%;
    --error-text: 0, 79%, 42%;
    --success: 128, 63%, 93%;
    --success-text: 131, 48%, 24%;
    --success-hover: 131, 58%, 36%;
    --delete: 0, 90%, 60%;
    --delete-foreground: 0, 100%, 82%;
    --warning: 46, 96%, 56%;
    --warning-bg: 51, 100%, 96%;
    --warning-text: 26, 82%, 31%;
    --info: 208, 100%, 59%;
    --info-bg: 201, 100%, 97%;
    --info-text: 215, 70%, 33%;
    --standard: 201, 100%, 97%;
    --standard-text: 215, 84%, 48%;
    --filter-text: 225, 10%, 47%;
    --cell-hover: 240, 8%, 97%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --shadow-light: rgba(9, 10, 13, 0.02);
    --shadow-medium: rgba(9, 10, 13, 0.1);
    --shadow-filter: 0px 0px 32px 0px var(--shadow-light), 0px 4px 20px -8px var(--shadow-medium);
    --disabled: 222, 6%, 67%;
    --overlay: rgba(9, 10, 13, 0.35);
    --box-icon-overlay: rgba(9, 10, 13, 0.40);
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer components {
  .border-border {
    border-color: hsl(var(--border));
  }

  .bg-background {
    background-color: hsl(var(--background));
  }

  .text-foreground {
    color: hsl(var(--foreground));
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Style scrollbar */
::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/* Track */
::-webkit-scrollbar-track {
  /* background: red; */
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: transparent;
  border-radius: 5px;
}

*:hover::-webkit-scrollbar-thumb {
  background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}

.scroll-bottom::-webkit-scrollbar-thumb {
  background: #888 !important;
}

/* Chrome, Safari, Edge, Opera */
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type='number'] {
  -moz-appearance: textfield;
}

body {
  pointer-events: all !important;
}

:where([data-sonner-toaster]) {
  font-family: inherit !important;
}



@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
