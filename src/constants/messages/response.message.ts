export const RESPONSE_MESSAGES = {
  //success
  UPDATE_SUCCESS: 'Cập nhật thành công',
  CREATE_SUCCESS: 'Tạo thành công',
  DELETE_SUCCESS: 'Xo<PERSON> thành công',
  PRINT_SUCCESS: 'Cập nhật thông tin in đơn thành công',
  SCAN_SUCCESS: 'Quét thành công',
  UPLOAD_SUCCESS: 'Up file thành công',
  //error
  TOO_LARGE_FILE_SIZE: 'Tải lên không thành công. File có kích thước quá lớn',
  ERROR_CONNECTION_SERVER: 'Lỗi kết nối server. Vui lòng thử lại sau!',
  PRINT_FAILED: 'Cập nhật thông tin in đơn thất bại',
  SCAN_FAILED: 'Quét thất bại',
  DUPLICATE_PHONE: '<PERSON><PERSON> điện thoại đã tồn tại. Vui lòng nhập SĐT khác',
  FETCH_ERROR: '<PERSON><PERSON><PERSON> dữ liệu thất bại',
  ITEM_EXIST: 'Item đã tồn tại',
  CREATE_ERROR: 'Tạo lỗi',
  UPDATE_ERROR: 'Cập nhật lỗi',
  DELETE_ERROR: 'Xoá lỗi',
  END_SESSION: 'Hết phiên làm việc',
  CONFIRM_ERROR: 'Xác nhận lỗi',
  UPLOAD_ERROR: 'Up file lỗi',
  //
  ERROR_500: 'Đã có lỗi 500 ở server',
  NOT_FOUND: 'Không tìm thấy thông tin!',
  MULTI_REQUEST: 'Hệ thống hạn chế hành động này trong 5 phút. Xin thực hiện lại sau!',
  ERROR: 'Đã có lỗi xảy ra',
  EXPORT_SUCCESS: 'Xuất file thành công',
  EXPORT_ERROR: 'Xuất file thất bại',
  ERROR_TRY_AGAIN: 'Có lỗi xảy ra. Vui lòng thử lại',
  // push data
  PUSH_DATA_SUCCESS: 'Đã đẩy dữ liệu thành công',
  PUSH_DATA_ERROR: 'Đã đẩy dữ liệu không thành công',
  IMPORT_SHEET_ERROR: 'Tạo phiếu nhập không thành công',
  // facebook
  SELECT_DEFAULT: 'Chọn làm tài khoản mặc định thành công',
  REMOVED_DEFAULT: 'Xoá tài khoản mặc định thành công',
};
