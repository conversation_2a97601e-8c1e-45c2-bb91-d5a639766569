import React from 'react';
import DataTable from '@/components/table/DataTable';
import { ColumnDef } from '@tanstack/react-table';
import { ITableSegmentList, TSegment } from '@/types/segment';
import { useTranslation } from 'react-i18next';

const TableSegmentList: React.FC<ITableSegmentList> = ({ ...props }: ITableSegmentList) => {
  const { columns, data, count, pagination, isLoading, setPagination } = props;
  const { t } = useTranslation();
  return (
    <DataTable
      data={data ?? []}
      columns={columns as ColumnDef<TSegment>[]}
      total={count}
      isShowFilter={true}
      isToolbar={false}
      isPerPageChange={true}
      classNameHeader="h-[52px] [&_th]:bg-secondary-foreground_crm"
      className="max-h-[700px]"
      loading={isLoading}
      pagination={pagination}
      searchPlaceHolder={t('common.searchByName')}
      setPagination={setPagination}
      filter={{
        isSearch: true,
        isFilter: false,
      }}
    />
  );
};
export default TableSegmentList;
