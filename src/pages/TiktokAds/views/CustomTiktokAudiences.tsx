import { useEffect, useState } from 'react';
import DataTable from '@/components/table/DataTable';
import { TFilterAudienceTiktok } from '@/types/facebook';
import { ICustomAudienceResponse } from '@/types/tiktok';
import FilterPanel from '@/pages/TiktokAds/components/FilterPanel';
import audienceCol from '@/pages/TiktokAds/components/Column/audienceCol';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import { useQuery } from '@tanstack/react-query';
import { QUERY_KEY } from '@/utils/constants';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { formatDateYYYYMMDD } from '@/utils/helper';

interface ICustomAudiences {
  filterPayload: TFilterAudienceTiktok;
  setFilterPayload: (value: TFilterAudienceTiktok) => void;
}

const CustomTiktokAudiences = ({ filterPayload, setFilterPayload }: ICustomAudiences) => {
  console.log('=>', filterPayload);
  const { adsAccount } = useTiktokContext();
  const [tiktokCustomAudience, setTiktokCustomAudience] = useState<ICustomAudienceResponse>({
    items: [],
    count: 0,
  });

  const [idActive, setIdActive] = useState<number>(0);

  const { data: customAudienceResponse, isLoading: loadingContact } = useQuery({
    queryKey: [QUERY_KEY.TIKTOK_AUDIENCE, filterPayload, adsAccount?.ad_account_id],
    enabled: !!adsAccount?.ad_account_id,
    staleTime: 1000,
    queryFn: () =>
      get({
        endpoint: ENDPOINTS.custom_audience[''],
        params: {
          ...filterPayload,
          ad_account_id: adsAccount?.ad_account_id,
        },
      }).then((res) => {
        if (res?.data?.code === 1001) {
          return {
            items: [],
            count: 0,
          };
        }
        return res.data?.data as ICustomAudienceResponse;
      }),
  });

  useEffect(() => {
    if (!!customAudienceResponse) {
      setTiktokCustomAudience(customAudienceResponse);
    }
  }, [customAudienceResponse]);

  // useEffect(() => {
  //   if (!ad_Account_id) {
  //     return;
  //   }
  //   handleGetAudience({
  //     ...filterPayload,
  //     ad_account_id: ad_Account_id,
  //   });
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [filterPayload, ad_Account_id]);

  // useEffect(() => {
  //   if (
  //     idActive > 0 &&
  //     listAudiences.find((item) => item.total_records === 0 || item.status === 'PENDING')
  //   ) {
  //     get({
  //       endpoint: ENDPOINTS.fb.log_detail(idActive),
  //     }).then((res) => {
  //       const dataAudience = res?.data?.data as unknown as IAudienceDetail;
  //       setListAudiences((prev) =>
  //         prev.map((item) => (item.job_id === dataAudience.job_id ? dataAudience : item)),
  //       );
  //     });
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [idActive]);

  return (
    <div className="flex flex-col">
      <FilterPanel
        setFilterPayload={(value) => {
          setFilterPayload({
            ...filterPayload,
            ...value,
            date_created_from: formatDateYYYYMMDD(value?.date_created_from ?? '', '-'),
            date_created_to: formatDateYYYYMMDD(value?.date_created_to ?? '', '-'),
          });
        }}
        filterPayload={filterPayload}
      />
      <DataTable
        className="h-[468px] [&_th]:bg-secondary-foreground_crm"
        data={tiktokCustomAudience.items}
        columns={audienceCol({ act: adsAccount?.ad_account_id || '', setIdActive })}
        total={tiktokCustomAudience.count}
        loading={loadingContact}
        pagination={{ pageSize: filterPayload.limit, currentPage: filterPayload.page }}
        setPagination={(value) => {
          setFilterPayload({ ...filterPayload, limit: value.pageSize, page: value.currentPage });
        }}
      />
    </div>
  );
};
export default CustomTiktokAudiences;
