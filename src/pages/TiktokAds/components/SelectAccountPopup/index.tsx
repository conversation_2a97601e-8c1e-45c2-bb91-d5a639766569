import Modal from '@/components/Modal';
import { LoadingButtonIcon } from '@/components/Loading/LoadingButton';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import TitlePopup from './TitlePopup';
import { ListBusiness } from '@/pages/TiktokAds/components/ListBusiness';

type Props = {
  open: boolean;
  setOpen?: (value: boolean) => void;
};

const Index = ({ open, setOpen }: Props) => {
  const { refetchLoading } = useTiktokContext();

  const onCloseModal = () => setOpen && setOpen(false);

  return (
    <Modal
      className="max-w-[786px] gap-6 max-h-svh overflow-auto"
      isCloseIcon={false}
      onOpenChange={setOpen}
      openModal={open}
      trigger={<></>}
    >
      <TitlePopup />
      {refetchLoading ? (
        <div className="w-full border-[1px] border-secondary shadow-sm rounded-2xl h-[484px] flex items-center justify-center">
          <LoadingButtonIcon width={'50px'} height={'50px'} />
        </div>
      ) : (
        <ListBusiness onCloseModal={onCloseModal} />
      )}
    </Modal>
  );
};
export default Index;
