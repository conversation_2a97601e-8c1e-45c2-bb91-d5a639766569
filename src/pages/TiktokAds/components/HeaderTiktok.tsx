// import AddAudiencePopup from './audiences/AddAudiencePopup';
import { useTiktokContext } from '../context/TiktokAuthContext';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  RiArrowDownSLine,
  RiArrowLeftRightLine,
  RiLinkUnlinkM,
  RiNewsLine,
} from '@remixicon/react';
import { useState } from 'react';
// import SelectAccountPopup from './SelectAccountPopup';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { TFilterAudience, TFilterAudienceTiktok } from '@/types/facebook';
import LogoutSocialPopup from '@/components/LogoutSocialPopup';
import SelectAccountPopup from '@/pages/TiktokAds/components/SelectAccountPopup';
import AddAudiencePopup from '@/components/CustomAudience/AddAudiencePopup';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';
import { tiktokOauthApi } from '@/apis/tiktokOauth';
import { QUERY_KEY } from '@/utils/constants';

interface IHeaderProps {
  filterPayload?: TFilterAudienceTiktok;
  setFilterPayload?: (value: TFilterAudienceTiktok) => void;
  ad_Account_id?: string;
}

const HeaderTiktok = ({ ...props }: IHeaderProps) => {
  const { setFilterPayload } = props;
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const { isLogin, isAccountSelected, adsAccount, logout, loading } = useTiktokContext();
  const [showModal, setShowModal] = useState(false);

  const mutationAdd = useMutation({
    mutationFn: tiktokOauthApi.uploadAudience,
    onSuccess: () => {
      if (setFilterPayload) {
        setFilterPayload({
          date_created_from: '',
          date_created_to: '',
          limit: 10,
          page: 1,
          search: '',
          platform: 'TT',
        });
      }

      queryClient.invalidateQueries({
        queryKey: [QUERY_KEY.TIKTOK_AUDIENCE]
      });

      toast({
        title: t('common.facebookAds.audiences.addCustomAudience'),
        status: 'success',
      });
    },
    onError: (e) => e,
  });

  const handleSubmit = (payload: { audience_name: string; segment_id: string }) => {
    const { audience_name, segment_id } = payload;
    mutationAdd.mutate({ audience_name, segment_id });
  };

  return (
    <div className="flex flex-col gap-0">
      <div className="text-left mb-4">
        <div className="flex-1 h-[64px]">
          <h3 className="text-xl font-semibold mb-2">{t('tiktokAds.title')}</h3>
          <p className="text-base font-normal text-tertiary-foreground tracking-[0.2px]">
            {t('tiktokAds.description')}
          </p>
        </div>
      </div>

      {isLogin && (
        <>
          <div
            className={cn(
              'flex items-center',
              isAccountSelected ? 'justify-between' : 'justify-end',
            )}
          >
            <div className="flex justify-center items-center w-[218px] h-10 px-[10px] border rounded-2xl border-big360Color-neutral-300">
              <div className="flex w-full justify-between items-center h-5 gap-2">
                <div className="flex gap-1 h-5 items-center w-full border-r border-big360Color-neutral-300">
                  <div className="w-6 h-6 flex items-center justify-center">
                    <RiNewsLine className="flex-shrink-0" size={16} color="#515667" />
                  </div>
                  <span className="truncate w-full text-start font-medium">
                    {!!adsAccount?.ad_account_id
                      ? adsAccount.ad_account_name
                      : t('common.facebookAds.switchAccount')}
                  </span>
                </div>
                <div>
                  <DropdownMenu>
                    <DropdownMenuTrigger>
                      <div className="bg-big360Color-neutral-100 flex h-8 w-8 border border-big360Color-neutral-100 items-center justify-center rounded-xl gap-2 p-2.5">
                        <RiArrowDownSLine className="ml-auto flex-shrink-0" size={20} />
                      </div>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent
                      className="w-[218px] p-2 flex flex-col gap-1"
                      align="end"
                      alignOffset={-10}
                      sideOffset={10}
                    >
                      {isAccountSelected && (
                        <DropdownMenuItem
                          className="cursor-pointer h-7"
                          onClick={() => setShowModal(true)}
                        >
                          <RiArrowLeftRightLine size={20} color={'#20232C'} />
                          <span className="font-medium">
                            {t('common.facebookAds.switchAccount')}
                          </span>
                        </DropdownMenuItem>
                      )}
                      <LogoutSocialPopup
                        onLogout={logout}
                        title={t('tiktokAds.disconnectTitle')}
                        description={t('tiktokAds.disconnectDescription')}
                        content={t('common.button.disconnect')}
                        icon={<RiLinkUnlinkM color={'#141416'} />}
                      />
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {isAccountSelected && (
                <AddAudiencePopup loading={loading} onSubmitAddSegment={handleSubmit} />
              )}
            </div>
          </div>
        </>
      )}
      {showModal && <SelectAccountPopup open={showModal} setOpen={setShowModal} />}
    </div>
  );
};
export default HeaderTiktok;
