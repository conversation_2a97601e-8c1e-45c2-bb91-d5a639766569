import { Box } from '@/components/Box';
import { Button } from '@/components/ui/button';
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import { toast } from '@/hooks/use-toast';
import { RESPONSE_MESSAGES } from '@/constants/messages/response.message';
import { cn } from '@/lib/utils';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import { tiktokOauthApi } from '@/apis/tiktokOauth';
import { IAdsAccount } from '@/types/tiktok';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

type TListBusinessProps = {
  onCloseModal?: () => void;
};

export const ListBusiness = ({ ...props }: TListBusinessProps) => {
  const { onCloseModal } = props;
  const { listPages, adsAccount, handleRefetchListPage, updateStateSelected } = useTiktokContext();
  const { t } = useTranslation();
  // const [loading, setLoading] = useState<boolean>(false);

  const handleSelectPage = async (account: IAdsAccount) => {
    const { ad_account_id, } = account;
    // if (selected) {
    //   return;
    // }
    // setLoading(true);
    try {
      await tiktokOauthApi.selectTiktokAdsAccount(ad_account_id);
      toast({
        status: 'success',
        description: RESPONSE_MESSAGES.SELECT_DEFAULT,
      });
      updateStateSelected(ad_account_id);
      handleRefetchListPage();
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      onCloseModal && onCloseModal();
      // setLoading(false);
    } catch (error) {
      // setLoading(false);
      return error;
    }
  };

  return (
    <Box className="h-[414px]">
      {!!listPages.length ? (
        <Box
          variant={'col-start'}
          className="flex-1 gap-2 rounded-2xl p-3 h-full border border-big360Color-neutral-300"
        >
          <div className="overflow-auto w-full h-full">
            {listPages.map((account) => {
              const { ad_account_id, ad_account_name } = account;
              return (
                <Box
                  key={ad_account_id}
                  className={cn('w-full p-2 justify-start rounded-xl hover:bg-big360Color-neutral-50')}
                >
                  <RadioGroup>
                    <div className="flex items-center w-fit space-x-2">
                      <RadioGroupItem
                        value={ad_account_id}
                        id={`phone-${ad_account_id}`}
                        checked={adsAccount?.ad_account_id === ad_account_id}
                        onClick={() => handleSelectPage(account)}
                        className={'border-brand'}
                      />
                    </div>
                  </RadioGroup>
                  <p className="flex flex-col text-left">
                    <span className="text-sm font-semibold text-primary-crm">
                      {ad_account_name}
                    </span>
                    <span className="text-xs font-normal text-secondary">{ad_account_id}</span>
                  </p>
                </Box>
              );
            })}
          </div>
          <Button onClick={handleRefetchListPage} className="w-full" variant={'outline'}>
            {t('common.button.reload')}
          </Button>
        </Box>
      ) : (
        <Box variant={'col-center'} className="flex-1 justify-center items-center">
          <div className="text-center">{t('common.facebookAds.account.selectYourBusiness')}</div>
          <Button onClick={handleRefetchListPage} className="w-fit" variant={'outline'}>
            {t('common.button.reload')}
          </Button>
        </Box>
      )}
    </Box>
  );
};
