import { useState, useCallback } from 'react';
import { TFilterAudienceTiktok } from '@/types/facebook';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import CustomTiktokAudiences from '@/pages/TiktokAds/views/CustomTiktokAudiences';
import HeaderTiktok from '@/pages/TiktokAds/components/HeaderTiktok';

const ContainerTiktokAds = () => {
  const [filterPayload, setFilterPayload] = useState<TFilterAudienceTiktok>({
    search: '',
    page: 1,
    limit: 10,
    date_created_from: '',
    date_created_to: '',
    platform: 'TT',
  });
  const { adsAccount } = useTiktokContext();

  // Wrapper function để đảm bảo force refetch khi cần
  const handleSetFilterPayload = useCallback((newPayload: TFilterAudienceTiktok) => {
    setFilterPayload(newPayload);
  }, []);
  return (
    <>
      <HeaderTiktok
        filterPayload={filterPayload}
        setFilterPayload={handleSetFilterPayload}
        ad_Account_id={adsAccount?.ad_account_id ?? ''}
      />

      <CustomTiktokAudiences filterPayload={filterPayload} setFilterPayload={handleSetFilterPayload} />
    </>
  );
};

export default ContainerTiktokAds;
