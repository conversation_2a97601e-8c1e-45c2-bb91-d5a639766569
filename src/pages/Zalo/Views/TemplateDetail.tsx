import { t } from 'i18next';
import PreviewZNS from '../components/PreviewZNS';
import { useContext, useEffect, useState } from 'react';
import SendForm from '../components/SendForm';
import { FormProvider, useForm } from 'react-hook-form';
import CreateTempForm from '../components/CreateTempForm';
import {
  initialTemplate,
  TZaloTempBeforeSchema,
  zaloTemplateSchema,
} from '@/constants/zalo/validate';
import { zodResolver } from '@hookform/resolvers/zod';
import { Link, useParams } from 'react-router-dom';
import { OauthContext } from '../Context/OauthContext';
import { zaloApi } from '@/apis/zaloApi';
import { Template } from '@/types/zalo';
import { fCustomForm } from '@/constants/zalo/form';
import LoadingScreen from '@/components/LoadingScreen';
import EmptyDataScreen from '@/components/EmptyDataScreen';
import PreviewDetail from '../components/Detail/PreviewDetail';
import { ROOT_PATH, ROOT_ROUTE } from '@/constants/router';
import { RiArrowLeftLine } from '@remixicon/react';

const TemplateDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { data: auth } = useContext(OauthContext);
  const [view, setView] = useState<'form' | 'send' | 'preview'>('preview');
  const [templateDetail, setTemplateDetail] = useState<Template | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  useEffect(() => {
    const fetchData = async () => {
      if (!id || !auth?.oa_id) return;
      setLoading(true);
      try {
        const res = await zaloApi.getTemplateDetail({ oa_id: auth.oa_id, temp_id: id });
        setTemplateDetail(res);
        setLoading(false);
      } catch (error) {
        setTemplateDetail(null);
        setLoading(false);
        return error;
      }
    };

    fetchData();
  }, [auth?.oa_id, id]);

  const methods = useForm<TZaloTempBeforeSchema>({
    resolver: zodResolver(zaloTemplateSchema),
    defaultValues: initialTemplate,
  });

  useEffect(() => {
    if (!templateDetail) return;
    if (templateDetail.data) {
      methods.reset(fCustomForm(templateDetail.data));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [templateDetail]);

  const toggle = () => setView(view === 'form' ? 'send' : 'form');
  const onClickEdit = () => setView('form');

  if (loading) return <LoadingScreen />;
  if (!templateDetail) return <EmptyDataScreen />;

  return (
    <div className="mt-3 flex flex-col gap-4">
      <Link
        className="flex items-center w-fit p-3 gap-1"
        to={`${ROOT_PATH}/${ROOT_ROUTE.zalo.zns}`}
      >
        <RiArrowLeftLine size={20} />
        {t('common.button.goBack')}
      </Link>
      <FormProvider {...methods}>
        <div className="flex gap-6">
          {view === 'preview' ? (
            <PreviewDetail onClickEdit={onClickEdit} status={templateDetail.status} />
          ) : (
            <div className="flex-1">
              {view === 'form' ? (
                <h6 className="font-medium leading-[26px] text-start my-6">
                  {t('common.zaloAds.createContent')}
                </h6>
              ) : (
                <div className="flex flex-col gap-1 items-start mb-4 mt-6">
                  <span className="font-medium">{t('common.zaloAds.sendForAppoval')}</span>
                  <span className="text-sm text-secondary">{t('common.zaloAds.description')}</span>
                </div>
              )}
              {view === 'form' && <CreateTempForm onClick={toggle} />}
              {view === 'send' && (
                <SendForm
                  onBack={toggle}
                  disableSend={!(templateDetail?.status === 'REJECT')}
                  isUpdate={true}
                />
              )}
            </div>
          )}

          <PreviewZNS />
        </div>
      </FormProvider>
    </div>
  );
};
export default TemplateDetail;
