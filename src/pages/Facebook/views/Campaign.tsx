import DataTable from '@/components/table/DataTable';
import { useRef, useState, useMemo } from 'react';
import campaignCol from '../components/colums/campaignCol';
import { useFBContext } from '../context/FbAuthContext';
import { TFilterCampaign } from '@/types/facebook';
import { t } from 'i18next';
import FilterPanelCampaign from '@/pages/Facebook/components/FilterPanelCampaign';
import { TCampaign } from '@/types/audience';
import { fbAudienceApi } from '@/apis/fbAudience';
import BodyTableInfinite from '@/components/BodyTableInfinite';
import { useInfiniteQuery } from '@tanstack/react-query';
import { deleteEmptyKey } from '@/utils/helper';

const Campaign = () => {
  const { isAccountSelected, listPages } = useFBContext();
  const isSelect = listPages.find((item) => item.selected);
  const [filterPayload, setFilterPayload] = useState<TFilterCampaign>({
    search: '',
    page: 1,
    limit: 10,
    date_preset: '',
    status: '',
    since: '',
    until: '',
  });

  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const queryKeyFilter = useMemo(() => {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { page: _, ...rest } = filterPayload;
    return rest;
  }, [filterPayload]);

  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetching,
    isLoading,
    refetch: refetchCampaigns,
  } = useInfiniteQuery({
    queryKey: ['campaigns', queryKeyFilter],
    enabled: isAccountSelected,
    queryFn: async ({ pageParam = 1, signal }) => {
      setFilterPayload((prev) => ({ ...prev, page: pageParam }));
      if (!isSelect) {
        return {
          items: [],
          nextPage: 0,
        };
      }
      const res = await fbAudienceApi.getCampaign(
        deleteEmptyKey({
          ...filterPayload,
          page: pageParam,
        }),
        signal,
      );

      return {
        items: res.data,
        nextPage: res.data.length < filterPayload.limit ? undefined : pageParam + 1,
      };
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => lastPage.nextPage,
    refetchOnMount: 'always',
    placeholderData: (prev) => prev,
  });

  const updateCampaign = async (campaign: Partial<TCampaign> = {}) => {
    return await fbAudienceApi.updateCampaign(campaign).then((res) => {
      refetchCampaigns();
      return res;
    });
  };

  const flatItems = data?.pages.flatMap((page) => page.items) || [];
  return (
    <div className="flex flex-col">
      <FilterPanelCampaign
        setFilterPayload={(value) => {
          setFilterPayload((prev) => ({
            ...prev,
            ...value,
            page: 1,
          }));
        }}
        filterPayload={filterPayload}
        placeHolder={t('common.facebookAds.date')}
      />
      <DataTable
        data={flatItems}
        columns={campaignCol({ updateCampaign })}
        total={flatItems.length}
        loading={isFetching}
        className="h-[468px] max-h-[468px] [&_th]:bg-secondary-foreground_crm"
        scrollRef={scrollContainerRef}
        body={(table) => (
          <BodyTableInfinite
            table={table}
            data={flatItems}
            columns={campaignCol({ updateCampaign })}
            isLoading={isLoading}
            isFetching={isFetching}
            className="min-h-[468px]"
            scrollContainerRef={scrollContainerRef}
            fetchNextPage={hasNextPage ? fetchNextPage : undefined}
            syncNoData={true}
          />
        )}
      />
    </div>
  );
};

export default Campaign;
