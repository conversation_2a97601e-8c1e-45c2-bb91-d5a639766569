import { useContext, useEffect, useState } from 'react';
import FilterPanel from '../components/FilterPanel';
import DataTable from '@/components/table/DataTable';
import audienceCol from '../components/colums/audienceCol';
import { AudienceContext } from '../context/AudienceContext';
import { IFbPage, TFilterAudience } from '@/types/facebook';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { IAudienceDetail } from '@/types/audience';

interface ICustomAudiences {
  filterPayload: TFilterAudience;
  setFilterPayload: (value: TFilterAudience) => void;
  listPages: IFbPage[];
}

const CustomAudiences = ({ filterPayload, setFilterPayload, listPages }: ICustomAudiences) => {
  const [ad_Account_id, setAd_Account_id] = useState<string>('');
  const { items, count, loading, handleGetAudience } = useContext(AudienceContext);
  const [listAudiences, setListAudiences] = useState<IAudienceDetail[]>(items);
  const [idActive, setIdActive] = useState<number>(0);

  const currentFbAccount = listPages.find((item) => item.selected);

  useEffect(() => {
    if (!!items.length) {
      setListAudiences(items);
    }
  }, [items]);

  useEffect(() => {
    if (!ad_Account_id) {
      return;
    }
    handleGetAudience({
      ...filterPayload,
      ad_account_id: ad_Account_id,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterPayload, ad_Account_id]);

  useEffect(() => {
    if (!listPages || !currentFbAccount) {
      return;
    }
    setAd_Account_id(currentFbAccount.id);
  }, [currentFbAccount, listPages]);

  useEffect(() => {
    if (idActive > 0 && listAudiences.find((item) => item.total_records === 0 || item.status === 'PENDING')) {
      get({
        endpoint: ENDPOINTS.fb.log_detail(idActive),
      }).then((res) => {
        const dataAudience = res?.data?.data as unknown as IAudienceDetail;
        setListAudiences((prev) =>
          prev.map((item) =>
            item.job_id === dataAudience.job_id? dataAudience : item,
          ),
        );
      });
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [idActive]);

  return (
    <div className="flex flex-col">
      <FilterPanel
        setFilterPayload={(value) => {
          setFilterPayload({ ...filterPayload, ...value });
        }}
        filterPayload={filterPayload}
      />
      <DataTable
        className="h-[468px] [&_th]:bg-secondary-foreground_crm"
        data={listAudiences}
        columns={audienceCol({ act: currentFbAccount?.account_id || '', setIdActive })}
        total={count}
        loading={loading}
        pagination={{ pageSize: filterPayload.limit, currentPage: filterPayload.page }}
        setPagination={(value) => {
          setFilterPayload({ ...filterPayload, limit: value.pageSize, page: value.currentPage });
        }}
      />
    </div>
  );
};
export default CustomAudiences;
