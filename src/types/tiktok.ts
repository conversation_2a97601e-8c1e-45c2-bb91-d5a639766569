import { IAudienceDetail } from '@/types/audience';

export interface ITiktokUser {
  ad_account_default: null;
  ad_accounts: string[];
  avatar_url: string;
  core_user_id: string;
  name: string;
  scope: number[];
  user_id: number;
}

export interface IAdsAccount {
  ad_account_id: string;
  ad_account_name: string;
}

export interface ICustomAudienceResponse {
  items: IAudienceDetail[];
  count: number;
}
