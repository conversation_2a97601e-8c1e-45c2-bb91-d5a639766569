import i18n from '@/config-translation';
import { IOptionsFilter } from '@/types/contactList';

export const MINUTE = 60 * 1000;

export const QUERY_KEY = {
  CONTACT_LIST: 'CONTACT_LIST',
  CONTACT_REPORT: 'CONTACT_REPORT',
  CONTACT_LIST_OVERDUE: 'CONTACT_LIST_OVERDUE',
  CONTACT_LIST_TRASH: 'CONTACT_LIST_TRASH',
  CONTACT_LIST_HISTORY: 'CONTACT_LIST_HISTORY',
  CONTACT_LIST_SEGMENT: 'CONTACT_LIST_SEGMENT',
  CONTACT_LIST_DETAIL_SEGMENT: 'CONTACT_LIST_DETAIL_SEGMENT',
  CONTACT_LIST_SEGMENT_CREATE: 'CONTACT_LIST_SEGMENT_CREATE',
  CONTACT_DETAIL: 'CONTACT_DETAIL',
  SEGMENT_LIST: 'SEGMENT_LIST',
  SEGMENT_DETAIL: 'SEGMENT_DETAIL',
  NOTIFICATIONS: 'NOTIFICATIONS',

  TIKTOK_AUDIENCE: 'TIKTOK_AUDIENCE',
};

export const PAGE_SIZE = 10;
export const MAX_LIMIT = 50;
export const MULTI_COL_CHECKBOX = 'MULTI_COL_CHECKBOX';
export const CHECKBOX = 'CHECKBOX';
export const RANGE = 'RANGE';

export const LABEL = {
  location: 'Location',
  type: 'Type',
  date: 'Date',
  time: 'Time',
  hour: 'Hour',
  minute: 'Minute',
  seconds: 'Seconds',
  duration: 'Duration',
  campaign: 'Campaign',
  pic: 'PIC',
  demographic: 'Demographic',
  age: 'Age',
  gender: 'Gender',
  dob: 'Birth Month',
  city: 'City',
  segment: 'Segment',
  status: 'Status',
  contactFrequency: 'Count Call',
  verifyNumber: 'Verify Number',
  month: 'Month',
  day: 'Day',
  year: 'Year',
  name: 'Name',
  phoneNumber: 'Phone Number',
  email: 'Email',
  address: 'Address',
  ward: 'Ward',
  district: 'District',
  company: 'Company',
  position: 'Position',
  contactHistory: 'Contact History',
  cityOrProvince: 'City/Province',
};

export const genderLabel = {
  male: i18n.t('common.male'),
  female: i18n.t('common.female'),
  other: i18n.t('common.other'),
};

export const statusLabel = {
  calling: i18n.t('common.calling'),
  valid: i18n.t('common.valid'),
  unverify: i18n.t('common.unverified'),
  invalid: i18n.t('common.invalid'),
  empty: i18n.t('common.empty'),
};

export const sortLabel = {
  dob: i18n.t('common.dob'),
  reminder: i18n.t('common.reminder.title'),
  gender: i18n.t('common.gender'),
};

export const monthLabel = {
  january: i18n.t('common.january'),
  february: i18n.t('common.february'),
  march: i18n.t('common.march'),
  april: i18n.t('common.april'),
  may: i18n.t('common.may'),
  june: i18n.t('common.june'),
  july: i18n.t('common.july'),
  august: i18n.t('common.august'),
  september: i18n.t('common.september'),
  october: i18n.t('common.october'),
  november: i18n.t('common.november'),
  december: i18n.t('common.december'),
};

export const ageSelectOptions = [
  { label: '<18', value: '<18' },
  { label: '18-24', value: '18-24' },
  { label: '25-34', value: '25-34' },
  { label: '35-44', value: '35-44' },
  { label: '45-54', value: '45-54' },
  { label: '55-64', value: '55-64' },
  { label: '65+', value: '65+' },
];
export const genderSelectOptions = [
  { label: genderLabel.male, value: 'male' },
  { label: genderLabel.female, value: 'female' },
  { label: genderLabel.other, value: 'other' },
];

export const statusSelectOptions = [
  { label: statusLabel.valid, value: 'confirmed' },
  { label: statusLabel.unverify, value: 'unconfirm' },
  { label: statusLabel.invalid, value: 'spam' },
];

export const emptySegment = {
  label: statusLabel.empty,
  value: 'empty',
  color: '',
};

export const sortSelectOptions = [
  { label: sortLabel.dob, value: 'dob' },
  { label: sortLabel.gender, value: 'gender' },
  { label: sortLabel.reminder, value: 'reminder__time_reminder' },
];

export const monthSelectOptions = [
  { label: monthLabel.january, value: '1' },
  { label: monthLabel.february, value: '2' },
  { label: monthLabel.march, value: '3' },
  { label: monthLabel.april, value: '4' },
  { label: monthLabel.may, value: '5' },
  { label: monthLabel.june, value: '6' },
  { label: monthLabel.july, value: '7' },
  { label: monthLabel.august, value: '8' },
  { label: monthLabel.september, value: '9' },
  { label: monthLabel.october, value: '10' },
  { label: monthLabel.november, value: '11' },
  { label: monthLabel.december, value: '12' },
];

export const paramsSearch = {
  gender__in: 'gender',
  age__in: 'age',
  status__in: 'status',
  dob_month__in: 'birthMonth',
  segment__in: 'segment',
  person_province__in: 'location',
  search: 'search',
  frequency: 'frequency',
  frequency_min: 'frequencyMin',
  frequency_max: 'frequencyMax',
};

export const keySearch = {
  gender: 'gender__in',
  age: 'age__in',
  status: 'status__in',
  dob: 'dob_month__in',
  segment: 'segment__in',
  location: 'person_province__in',
  search: 'search',
};

export const OptionFilterDemoGraphic = [
  {
    title: i18n.t('common.age'),
    titleCode: LABEL.age,
    type: MULTI_COL_CHECKBOX,
    options: ageSelectOptions,
  },
  {
    title: i18n.t('common.gender'),
    titleCode: LABEL.gender,
    type: MULTI_COL_CHECKBOX,
    options: genderSelectOptions,
  },
] as IOptionsFilter[];

export const RangeOptions = [
  { label: 'min', value: '0' },
  { label: 'max', value: '0' },
];

export const regexPhone = new RegExp(/^0([0-9]|3[2-9]|5[6|8|9]|7[0|6-9]|8[1-9]|9[0-9])[0-9]{7}$/);

export const HISTORY_CALL_STATUS = {
  meet_call: i18n.t('contactHistory.meetCall'),
  miss_call: i18n.t('contactHistory.missCall'),
  stop_at_IVR: i18n.t('contactHistory.stopAtIVR'),
  stop_at_survey_IVR: i18n.t('contactHistory.stopAtSurveyIVR'),
};

export enum HISTORY_CALL_STATUS_ENUM {
  all = 'all',
  outgoing = 'outgoing',
  zalo = 'zalo',
  facebook = 'facebook',
}

export const ZALO_HOW_TO_CONNECT = {
  createZCA: 'https://oa.zalo.me/home/<USER>/vie/guides/huong-dan-tao-ZCA_75',
  appIdToZCA:
    'https://zalo.cloud/blog/huong-dan-lien-ket-app-id-vao-tai-khoan-zalo-cloud-account-zca/g6c2pw3grmirqedi1t69u6ta',
  zaloDev: 'https://developers.zalo.me/',
  webHook: 'https://crmdevbe.big360.ai/api/v1/zalo/webhook/',
  prodEnv: 'https://crmbe.big360.ai/api/v1/oauth/zalo/callback/',
  devEnv: 'https://crmdevbe.big360.ai/api/v1/oauth/zalo/callback/',
};

export const DISABLE_DIALOG_SCROLL = 'dialog-disable-scroll';
