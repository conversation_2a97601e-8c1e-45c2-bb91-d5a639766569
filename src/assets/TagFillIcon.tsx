import React from 'react';

type TTagFillIconProps = {
  color?: string;
  size?: number;
};
export const TagFillIcon: React.FC<TTagFillIconProps> = ({ ...props }: TTagFillIconProps) => {
  const { color, size = 12 } = props;
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      style={{
        minWidth: size,
        minHeight: size,
      }}
      viewBox="0 0 12 13"
      fill="none"
    >
      <circle cx="6" cy="6.63477" r="6" fill={color?.includes('#') ? color : '#8F5CFF'} />
    </svg>
  );
};
